# 固件头部适配指南

## 概述

Bootloader已经成功适配了新的20字节固件头部结构。现在固件文件的格式如下：

```
[20字节头部] + [应用程序代码]
```

### 头部结构 (20字节)
```c
typedef struct {
    uint32_t version;       // 固件版本号 (4字节)
    uint32_t size;          // 固件大小，不包括头部 (4字节)  
    uint32_t crc;           // 固件CRC32值 (4字节)
    uint32_t reserved[2];   // 保留字段 (8字节)
} __attribute__((packed)) firmware_header_t;
```

## 主要修改

### 1. 地址定义更新
- `APP_BASE_ADDRESS`: 0x08005000 (固件起始地址，包含头部)
- `APP_CODE_ADDRESS`: 0x08005014 (实际代码起始地址，跳过20字节头部)

### 2. 新增函数

#### 头部操作函数
- `read_firmware_header()`: 读取固件头部信息
- `validate_firmware_header()`: 验证头部有效性
- `verify_app_crc_with_header()`: 使用头部信息验证CRC
- `print_firmware_header_info()`: 打印头部信息(调试用)

#### 跳转函数
- `iap_load_app_with_header()`: 自动处理头部的应用程序跳转

### 3. 更新的函数
- `is_app_valid()`: 现在检查实际代码区域的向量表
- `get_app_size()`: 优先从头部读取大小信息
- `verify_firmware_after_update()`: 支持头部结构验证

## 使用方法

### 1. 跳转到应用程序
```c
// 推荐方式：自动处理头部
iap_load_app_with_header();

// 或者使用兼容方式
iap_load_app_with_crc_check(APP_BASE_ADDRESS);
```

### 2. 验证固件
```c
// 检查应用程序是否有效（包含头部验证）
if (is_app_valid()) {
    printf("Firmware is valid\n");
}

// 单独验证CRC
if (verify_app_crc_with_header()) {
    printf("CRC verification passed\n");
}
```

### 3. 调试信息
```c
// 打印头部详细信息
print_firmware_header_info();
```

## 固件生成要求

固件生成工具需要：
1. 在固件开头添加20字节头部
2. 头部包含版本号、代码大小、CRC值
3. CRC计算范围：跳过头部的应用程序代码部分
4. 所有数值使用小端格式存储

## 兼容性

- 保持了与旧版本的兼容性
- 如果检测不到有效头部，会回退到扫描方式
- 现有的CAN命令和协议保持不变

## 测试建议

1. 使用 `print_firmware_header_info()` 验证头部读取
2. 检查 `is_app_valid()` 的返回结果
3. 确认应用程序能正常跳转和运行
4. 验证CRC检查功能正常工作
