/**
 * @file app_crc_verify.h
 * @brief 应用程序CRC验证功能
 * @date 2025-08-22
 * <AUTHOR>
 */

#ifndef __APP_CRC_VERIFY_H__
#define __APP_CRC_VERIFY_H__

#include <stdint.h>
#include <stdbool.h>
#include "stm32f1xx_hal.h"

/* 应用程序基地址 (与IAP模块保持一致) */
#define APP_BASE_ADDRESS    0x08005000

/* CRC32计算参数 (与Python脚本保持一致) */
#define CRC32_POLYNOMIAL    0x04C11DB7
#define CRC32_INIT_VALUE    0x00000000

/* 应用程序最大大小 (根据Flash布局设定) */
#define MAX_APP_SIZE        0x1B000  // 108KB

/* 固件头部结构定义 */
#define FIRMWARE_HEADER_SIZE    20      // 固件头部大小
#define FIRMWARE_VERSION_OFFSET 0       // 版本号偏移
#define FIRMWARE_SIZE_OFFSET    4       // 固件大小偏移
#define FIRMWARE_CRC_OFFSET     8       // CRC偏移
#define FIRMWARE_RESERVED_OFFSET 12     // 保留字段偏移

/* 实际应用程序起始地址 (跳过20字节头部) */
#define APP_CODE_ADDRESS    (APP_BASE_ADDRESS + FIRMWARE_HEADER_SIZE)

/**
 * @brief 固件头部结构体
 */
typedef struct {
    uint32_t version;       // 固件版本号
    uint32_t size;          // 固件大小 (不包括头部)
    uint32_t crc;           // 固件CRC32值
    uint32_t reserved[2];   // 保留字段 (8字节)
} __attribute__((packed)) firmware_header_t;

/**
 * @brief 计算数据的CRC32值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC32值
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length);

/**
 * @brief 验证应用程序的CRC
 * @param app_start_addr 应用程序起始地址
 * @param app_size 应用程序总大小 (包括CRC的4字节)
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_size);

/**
 * @brief 获取应用程序的实际大小
 * @return 应用程序大小 (字节), 0表示无效
 */
uint32_t get_app_size(void);

/**
 * @brief 检查应用程序是否有效（包括CRC验证）
 * @return true: 应用程序有效, false: 应用程序无效
 */
bool is_app_valid(void);

/**
 * @brief 扫描Flash找到实际的应用程序大小
 * @param start_addr 扫描起始地址
 * @param max_size 最大扫描大小
 * @return 实际大小，如果无法确定则返回0
 */
uint32_t scan_app_size(uint32_t start_addr, uint32_t max_size);

/**
 * @brief 读取固件头部信息
 * @param header 固件头部结构体指针
 * @return true: 读取成功, false: 读取失败
 */
bool read_firmware_header(firmware_header_t *header);

/**
 * @brief 验证固件头部的有效性
 * @param header 固件头部结构体指针
 * @return true: 头部有效, false: 头部无效
 */
bool validate_firmware_header(const firmware_header_t *header);

/**
 * @brief 使用头部信息验证应用程序CRC
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc_with_header(void);

/**
 * @brief 打印固件头部信息 (调试用)
 */
void print_firmware_header_info(void);

#endif /* __APP_CRC_VERIFY_H__ */
