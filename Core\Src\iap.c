#include "sys.h"
#include "usart.h"
#include "stmflash.h"
#include "iap.h"
#include "app_crc_verify.h"  // 添加CRC验证支持
#include "stdio.h"

iapfun jump2app;
uint16_t iapbuf[512];
//appxaddr: 应用程序地址
//appbuf: 应用程序CODE数据
//appsize: 应用程序大小(字节)
void iap_write_appbin ( uint32_t appxaddr, uint8_t* appbuf, uint32_t appsize )
{
    uint16_t t;
    uint16_t i = 0;
    uint16_t temp;
    uint32_t fwaddr = appxaddr; //当前写入的地址
    uint8_t* dfu = appbuf;
    for ( t = 0; t < appsize; t += 2 )
    {
        temp = ( uint16_t ) dfu[1] << 8;
        temp += ( uint16_t ) dfu[0];
        dfu += 2; //偏移2个字节
        iapbuf[i++] = temp;
        if ( i == 512 )
        {
            i = 0;
            STMFLASH_Write ( fwaddr, iapbuf, 512 );
            fwaddr += 1024;	//偏移1024字节
        }
    }
    if ( i ) STMFLASH_Write ( fwaddr, iapbuf, i ); //将剩余的一些数据字节写进去
}

//跳转到应用程序
//appxaddr:用户代码起始地址
void iap_load_app ( uint32_t appxaddr )
{
    uint32_t stack_ptr = *(volatile uint32_t*)appxaddr;
    uint32_t reset_vector = *(volatile uint32_t*)(appxaddr + 4);

    printf("Jump to app debug info:\r\n");
    printf("  App address: 0x%08lX\r\n", appxaddr);
    printf("  Stack pointer: 0x%08lX\r\n", stack_ptr);
    printf("  Reset vector: 0x%08lX\r\n", reset_vector);

    if ( ( stack_ptr & 0x2FFE0000 ) == 0x20000000 )	//检查栈顶地址是否合法
    {
        printf("  Stack pointer check: PASSED\r\n");

        // 检查复位向量是否合法
        if ((reset_vector & 0xFF000000) != 0x08000000) {
            printf("  Reset vector check: FAILED - not in Flash (0x%08lX)\r\n", reset_vector);
            return;
        }

        // 检查复位向量是否为奇数（Thumb模式）
        if ((reset_vector & 0x01) == 0) {
            printf("  Reset vector check: FAILED - not Thumb mode (0x%08lX)\r\n", reset_vector);
            return;
        }

        printf("  Reset vector check: PASSED (Thumb mode)\r\n");

        printf("  Preparing to jump...\r\n");

        // 等待串口输出完成
        HAL_Delay(100);

        printf("  Step 1: Disabling interrupts...\r\n");
        HAL_Delay(10);
        __disable_irq();

        printf("  Step 2: Resetting peripherals...\r\n");
        HAL_Delay(10);

        // 关闭SysTick
        SysTick->CTRL = 0;
        SysTick->LOAD = 0;
        SysTick->VAL = 0;

        uint32_t vector_offset = appxaddr - 0x08000000;  // 计算相对于Flash基地址的偏移
        printf("  Step 3: Setting vector table offset to 0x%08lX (absolute: 0x%08lX)...\r\n", vector_offset, appxaddr);
        HAL_Delay(10);
        // 设置向量表偏移寄存器 (相对偏移)
        SCB->VTOR = vector_offset;

        printf("  Step 4: Setting stack pointer to 0x%08lX...\r\n", stack_ptr);
        HAL_Delay(10);
        __set_MSP ( stack_ptr );					//初始化APP堆栈指针

        printf("  Step 5: Setting jump address to 0x%08lX...\r\n", reset_vector);
        HAL_Delay(10);
        jump2app = ( iapfun ) reset_vector;		//设置跳转地址

        printf("  Step 6: Final jump...\r\n");
        HAL_Delay(50);  // 确保串口输出完成

        // 最后的跳转
        jump2app();									//跳转到APP
    }
    else
    {
        printf("  Stack pointer check: FAILED (0x%08lX)\r\n", stack_ptr);
    }
}

// 写入单个数据块到Flash
uint8_t iap_write_block(uint32_t flash_addr, uint8_t *data, uint32_t len)
{
    HAL_StatusTypeDef status = HAL_OK;
    
    printf("Debug: Writing %d bytes to 0x%08X\r\n", len, flash_addr);
    
    // 解锁Flash
    HAL_FLASH_Unlock();

    // 按字节写入
    for (uint32_t i = 0; i < len; i += 2)
    {
        uint16_t data_to_write;
        
        if (i + 1 < len) {
            // 组合成16位数据
            data_to_write = (uint16_t)data[i + 1] << 8 | (uint16_t)data[i];
        } else {
            // 最后一个字节，高位补0
            data_to_write = (uint16_t)data[i];
        }

        // 写入Flash
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_HALFWORD, 
                                 flash_addr + i, 
                                 data_to_write);
        
        if (status != HAL_OK) {
            printf("Flash write error at address 0x%08X, status: %d\r\n", 
                   flash_addr + i, status);
            HAL_FLASH_Lock();
            return 1; // 写入失败
        }
    }

    // 锁定Flash
    HAL_FLASH_Lock();
    
    printf("Debug: Successfully wrote %d bytes\r\n", len);
    return 0; // 写入成功
}

// 擦除APP区域Flash(108KB)
void iap_erase_app_area(void)
{
    FLASH_EraseInitTypeDef EraseInitStruct;
    uint32_t PAGEError = 0;
    HAL_StatusTypeDef status;
    
    printf("Starting Flash erase...\r\n");
    
    // 解锁Flash
    HAL_FLASH_Unlock();

    // 设置擦除参数
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_PAGES;
    EraseInitStruct.PageAddress = FLASH_APP1_ADDR;  // 从0x08005000开始
    EraseInitStruct.NbPages = 108;  // 擦除108个页面(108KB)，每页1KB

    // 执行擦除
    status = HAL_FLASHEx_Erase(&EraseInitStruct, &PAGEError);
    
    if (status == HAL_OK) {
        printf("Flash erase completed successfully! Erased 108KB from 0x%08X\r\n", FLASH_APP1_ADDR);
    } else {
        printf("Flash erase failed! Error code: %d, Page error: 0x%08X\r\n", status, PAGEError);
    }
    
    // 锁定Flash
    HAL_FLASH_Lock();
}

/**
 * @brief 执行带头部的应用程序 (自动跳过20字节头部)
 */
void iap_load_app_with_header(void)
{
    printf("Loading application with firmware header...\r\n");

    // 首先验证应用程序的完整性
    if (!is_app_valid()) {
        printf("Application validation failed, cannot jump to app\r\n");
        return;
    }

    printf("Application validation passed, jumping to app code...\r\n");
    printf("App code address: 0x%08lX\r\n", APP_CODE_ADDRESS);

    // 跳转到实际的应用程序代码 (跳过20字节头部)
    iap_load_app(APP_CODE_ADDRESS);
}

//跳转到应用程序 (带CRC检查) - 支持头部固件
void iap_load_app_with_crc_check(uint32_t appxaddr)
{
    printf("开始进行带CRC检查的跳转...\r\n");

    // 检查应用程序是否有效
    if (!is_app_valid()) {
        printf("应用程序CRC验证失败，无法跳转!\r\n");
        return;
    }

    printf("应用程序CRC验证通过，开始跳转...\r\n");

    // 检查是否为带头部的固件
    firmware_header_t header;
    if (appxaddr == APP_BASE_ADDRESS && read_firmware_header(&header) && validate_firmware_header(&header)) {
        // 带头部固件：跳转到实际代码地址（跳过20字节头部）
        printf("Detected firmware with header, jumping to code area: 0x%08lX\r\n", APP_CODE_ADDRESS);
        iap_load_app(APP_CODE_ADDRESS);
    } else {
        // 无头部固件或直接指定地址：直接跳转
        printf("Jumping to specified address: 0x%08lX\r\n", appxaddr);
        iap_load_app(appxaddr);
    }
}














