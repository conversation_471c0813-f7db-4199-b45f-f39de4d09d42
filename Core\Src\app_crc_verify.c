/**
 * @file app_crc_verify.c
 * @brief 应用程序CRC验证功能实现
 * @date 2025-08-22
 * <AUTHOR>
 */

#include "app_crc_verify.h"
#include "stmflash.h"
#include "usart.h"
#include "stdio.h"

/**
 * @brief 计算数据的CRC32值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC32值
 * 
 * @note 使用与Python脚本相同的参数:
 *       - 多项式: 0x04C11DB7
 *       - 初值: 0x00000000
 *       - 不反转输入
 *       - 不反转输出
 *       - 不异或输出
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length)
{
    uint32_t crc = CRC32_INIT_VALUE;
    uint32_t i, j;
    
    for (i = 0; i < length; i++) {
        uint8_t byte = data[i];
        
        // 将字节移到高位
        crc ^= (uint32_t)byte << 24;
        
        // 处理每一位
        for (j = 0; j < 8; j++) {
            if (crc & 0x80000000) {
                crc = (crc << 1) ^ CRC32_POLYNOMIAL;
            } else {
                crc = crc << 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 验证应用程序的CRC
 * @param app_start_addr 应用程序起始地址
 * @param app_size 应用程序总大小 (包括CRC的4字节)
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_size)
{
    // 参数检查
    if (app_size < 4) {
        printf("CRC check failed: firmware size too small (%lu bytes)\r\n", app_size);
        return false;
    }
    
    if (app_size > MAX_APP_SIZE) {
        printf("CRC check failed: firmware size exceeds limit (%lu > %lu)\r\n", app_size, MAX_APP_SIZE);
        return false;
    }
    
    // 计算应用程序数据大小 (总大小 - CRC的4字节)
    uint32_t app_data_size = app_size - 4;
    
    printf("Starting CRC check...\r\n");
    printf("App address: 0x%08lX\r\n", app_start_addr);
    printf("App size: %lu bytes\r\n", app_data_size);
    
    // 获取存储在末尾的CRC值 (小端格式)
    uint8_t *crc_addr = (uint8_t *)(app_start_addr + app_data_size);
    uint32_t stored_crc = 0;
    
    // 从小端字节序转换为32位值
    stored_crc = (uint32_t)crc_addr[0] |
                 ((uint32_t)crc_addr[1] << 8) |
                 ((uint32_t)crc_addr[2] << 16) |
                 ((uint32_t)crc_addr[3] << 24);
    
    printf("Stored CRC: 0x%08lX\r\n", stored_crc);
    
    // 计算应用程序数据的CRC (不包括末尾的CRC值)
    uint32_t calculated_crc = calculate_crc32((uint8_t *)app_start_addr, app_data_size);
    
    printf("Calculated CRC: 0x%08lX\r\n", calculated_crc);
    
    // 比较CRC值
    if (stored_crc == calculated_crc) {
        printf("CRC check passed!\r\n");
        return true;
    } else {
        printf("CRC check failed! (Stored: 0x%08lX, Calculated: 0x%08lX)\r\n", stored_crc, calculated_crc);
        return false;
    }
}

/**
 * @brief 扫描Flash找到实际的应用程序大小
 * @param start_addr 扫描起始地址
 * @param max_size 最大扫描大小
 * @return 实际大小，如果无法确定则返回0
 */
uint32_t scan_app_size(uint32_t start_addr, uint32_t max_size)
{
    uint32_t size = 0;
    uint8_t *ptr = (uint8_t *)start_addr;
    
    // 从后往前扫描，找到第一个非0xFF的位置
    for (uint32_t i = max_size; i > 0; i--) {
        if (ptr[i - 1] != 0xFF) {
            size = i;
            break;
        }
    }
    
    // 检查是否找到了数据
    if (size == 0) {
        printf("Scan failed: no valid data found\r\n");
        return 0;
    }
    
    // 确保大小是4的倍数（CRC对齐）
    if (size % 4 != 0) {
        size = ((size / 4) + 1) * 4;
    }
    
    printf("Scanned app size: %lu bytes\r\n", size);
    return size;
}

/**
 * @brief 读取固件头部信息
 * @param header 固件头部结构体指针
 * @return true: 读取成功, false: 读取失败
 */
bool read_firmware_header(firmware_header_t *header)
{
    if (header == NULL) {
        printf("Error: header pointer is NULL\r\n");
        return false;
    }

    // 从Flash读取头部信息
    uint8_t *header_addr = (uint8_t *)APP_BASE_ADDRESS;

    // 读取版本号 (小端格式)
    header->version = (uint32_t)header_addr[0] |
                     ((uint32_t)header_addr[1] << 8) |
                     ((uint32_t)header_addr[2] << 16) |
                     ((uint32_t)header_addr[3] << 24);

    // 读取固件大小 (小端格式)
    header->size = (uint32_t)header_addr[4] |
                  ((uint32_t)header_addr[5] << 8) |
                  ((uint32_t)header_addr[6] << 16) |
                  ((uint32_t)header_addr[7] << 24);

    // 读取CRC (小端格式)
    header->crc = (uint32_t)header_addr[8] |
                 ((uint32_t)header_addr[9] << 8) |
                 ((uint32_t)header_addr[10] << 16) |
                 ((uint32_t)header_addr[11] << 24);

    // 读取保留字段
    for (int i = 0; i < 2; i++) {
        int offset = 12 + i * 4;
        header->reserved[i] = (uint32_t)header_addr[offset] |
                             ((uint32_t)header_addr[offset + 1] << 8) |
                             ((uint32_t)header_addr[offset + 2] << 16) |
                             ((uint32_t)header_addr[offset + 3] << 24);
    }

    printf("Firmware header read:\r\n");
    printf("  Version: 0x%08lX\r\n", header->version);
    printf("  Size: %lu bytes\r\n", header->size);
    printf("  CRC: 0x%08lX\r\n", header->crc);

    return true;
}

/**
 * @brief 验证固件头部的有效性
 * @param header 固件头部结构体指针
 * @return true: 头部有效, false: 头部无效
 */
bool validate_firmware_header(const firmware_header_t *header)
{
    if (header == NULL) {
        printf("Error: header pointer is NULL\r\n");
        return false;
    }

    // 检查固件大小是否合理
    if (header->size == 0) {
        printf("Header validation failed: firmware size is 0\r\n");
        return false;
    }

    if (header->size > (MAX_APP_SIZE - FIRMWARE_HEADER_SIZE)) {
        printf("Header validation failed: firmware size too large (%lu > %lu)\r\n",
               header->size, MAX_APP_SIZE - FIRMWARE_HEADER_SIZE);
        return false;
    }

    // 检查版本号是否合理 (可以根据需要添加更多检查)
    if (header->version == 0xFFFFFFFF) {
        printf("Header validation failed: invalid version 0x%08lX\r\n", header->version);
        return false;
    }

    printf("Firmware header validation passed\r\n");
    return true;
}

/**
 * @brief 使用头部信息验证应用程序CRC
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc_with_header(void)
{
    firmware_header_t header;

    // 读取固件头部
    if (!read_firmware_header(&header)) {
        printf("Failed to read firmware header\r\n");
        return false;
    }

    // 验证头部有效性
    if (!validate_firmware_header(&header)) {
        printf("Firmware header validation failed\r\n");
        return false;
    }

    printf("Starting CRC verification with header info...\r\n");
    printf("App code address: 0x%08lX\r\n", APP_CODE_ADDRESS);
    printf("App code size: %lu bytes\r\n", header.size);
    printf("Expected CRC: 0x%08lX\r\n", header.crc);

    // 计算应用程序代码的CRC (跳过20字节头部)
    uint32_t calculated_crc = calculate_crc32((uint8_t *)APP_CODE_ADDRESS, header.size);

    printf("Calculated CRC: 0x%08lX\r\n", calculated_crc);

    // 比较CRC值
    if (header.crc == calculated_crc) {
        printf("CRC verification passed!\r\n");
        return true;
    } else {
        printf("CRC verification failed! (Expected: 0x%08lX, Calculated: 0x%08lX)\r\n",
               header.crc, calculated_crc);
        return false;
    }
}

/**
 * @brief 获取应用程序的实际大小 (兼容旧版本)
 * @return 应用程序大小 (字节), 0表示无效
 */
uint32_t get_app_size(void)
{
    firmware_header_t header;

    // 尝试读取固件头部
    if (read_firmware_header(&header) && validate_firmware_header(&header)) {
        // 返回总大小：头部 + 应用程序代码
        return FIRMWARE_HEADER_SIZE + header.size;
    }

    // 如果头部读取失败，回退到扫描方式
    printf("Header read failed, falling back to scanning method...\r\n");
    return scan_app_size(APP_BASE_ADDRESS, MAX_APP_SIZE);
}

/**
 * @brief 检查应用程序是否有效（包括向量表和CRC验证）
 * @return true: 应用程序有效, false: 应用程序无效
 */
bool is_app_valid(void)
{
    printf("==========================================\r\n");
    printf("Starting app integrity check...\r\n");

    // 1. 检查向量表（栈指针） - 现在检查实际代码区域
    uint32_t stack_ptr = *(volatile uint32_t*)APP_CODE_ADDRESS;
    if ((stack_ptr & 0x2FFE0000) != 0x20000000) {
        printf("Vector table check failed: invalid stack pointer 0x%08lX\r\n", stack_ptr);
        return false;
    }
    printf("Vector table check passed: stack pointer 0x%08lX\r\n", stack_ptr);

    // 2. 检查复位向量
    uint32_t reset_vector = *(volatile uint32_t*)(APP_CODE_ADDRESS + 4);
    if ((reset_vector & 0xFF000000) != 0x08000000) {
        printf("Vector table check failed: invalid reset vector 0x%08lX\r\n", reset_vector);
        return false;
    }
    printf("Vector table check passed: reset vector 0x%08lX\r\n", reset_vector);

    // 3. 读取并验证固件头部
    firmware_header_t header;
    if (!read_firmware_header(&header)) {
        printf("Failed to read firmware header\r\n");
        printf("App integrity check failed!\r\n");
        printf("==========================================\r\n");
        return false;
    }

    if (!validate_firmware_header(&header)) {
        printf("Firmware header validation failed\r\n");
        printf("App integrity check failed!\r\n");
        printf("==========================================\r\n");
        return false;
    }

    // 4. 进行CRC验证
    bool crc_valid = verify_app_crc_with_header();

    if (crc_valid) {
        printf("App integrity check passed!\r\n");
        printf("==========================================\r\n");
        return true;
    } else {
        printf("App integrity check failed!\r\n");
        printf("==========================================\r\n");
        return false;
    }
}

/**
 * @brief 验证固件更新完成后的完整性
 * @param total_size 接收到的固件总大小 (包括20字节头部)
 * @return true: 验证通过, false: 验证失败
 */
bool verify_firmware_after_update(uint32_t total_size)
{
    printf("==========================================\r\n");
    printf("Firmware update completed, starting verification...\r\n");
    printf("Received firmware size: %lu bytes (including header)\r\n", total_size);

    // 检查大小是否合理 (至少要有头部大小)
    if (total_size < FIRMWARE_HEADER_SIZE) {
        printf("Firmware size too small: %lu bytes (minimum: %d bytes)\r\n",
               total_size, FIRMWARE_HEADER_SIZE);
        return false;
    }

    if (total_size > MAX_APP_SIZE) {
        printf("Firmware size exceeds limit: %lu > %lu bytes\r\n", total_size, MAX_APP_SIZE);
        return false;
    }

    // 读取并验证固件头部
    firmware_header_t header;
    if (!read_firmware_header(&header)) {
        printf("Failed to read firmware header after update\r\n");
        printf("==========================================\r\n");
        return false;
    }

    if (!validate_firmware_header(&header)) {
        printf("Firmware header validation failed after update\r\n");
        printf("==========================================\r\n");
        return false;
    }

    // 检查头部中的大小与接收到的大小是否一致
    uint32_t expected_total_size = FIRMWARE_HEADER_SIZE + header.size;
    if (total_size != expected_total_size) {
        printf("Size mismatch: received %lu bytes, header indicates %lu bytes\r\n",
               total_size, expected_total_size);
        // 这里可以选择是否要严格检查，或者使用头部中的大小
        printf("Warning: Using size from header (%lu bytes)\r\n", expected_total_size);
    }

    // 进行CRC验证
    bool result = verify_app_crc_with_header();

    if (result) {
        printf("Firmware verification passed! Safe to start the application.\r\n");
        printf("Firmware version: 0x%08lX\r\n", header.version);
        printf("Application code size: %lu bytes\r\n", header.size);
    } else {
        printf("Firmware verification failed! Please re-burn the firmware.\r\n");
    }

    printf("==========================================\r\n");
    return result;
}

/**
 * @brief 打印固件头部信息 (调试用)
 */
void print_firmware_header_info(void)
{
    firmware_header_t header;

    printf("==========================================\r\n");
    printf("Firmware Header Information:\r\n");
    printf("Header address: 0x%08lX\r\n", APP_BASE_ADDRESS);
    printf("Code address: 0x%08lX\r\n", APP_CODE_ADDRESS);

    if (read_firmware_header(&header)) {
        printf("Header read successfully:\r\n");
        printf("  Version: 0x%08lX (%lu)\r\n", header.version, header.version);
        printf("  Size: %lu bytes (%.2f KB)\r\n", header.size, (float)header.size / 1024.0f);
        printf("  CRC: 0x%08lX\r\n", header.crc);
        printf("  Reserved[0]: 0x%08lX\r\n", header.reserved[0]);
        printf("  Reserved[1]: 0x%08lX\r\n", header.reserved[1]);

        if (validate_firmware_header(&header)) {
            printf("Header validation: PASSED\r\n");
        } else {
            printf("Header validation: FAILED\r\n");
        }
    } else {
        printf("Failed to read firmware header\r\n");
    }

    printf("==========================================\r\n");
}
