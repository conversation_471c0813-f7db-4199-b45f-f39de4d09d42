/**
 * @file app_crc_verify.c
 * @brief 应用程序CRC验证功能实现
 * @date 2025-08-22
 * <AUTHOR>
 */

#include "app_crc_verify.h"
#include "stmflash.h"
#include "usart.h"
#include "stdio.h"

/**
 * @brief 计算数据的CRC32值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC32值
 * 
 * @note 使用与Python脚本相同的参数:
 *       - 多项式: 0x04C11DB7
 *       - 初值: 0x00000000
 *       - 不反转输入
 *       - 不反转输出
 *       - 不异或输出
 */
uint32_t calculate_crc32(const uint8_t *data, uint32_t length)
{
    uint32_t crc = CRC32_INIT_VALUE;
    uint32_t i, j;
    
    for (i = 0; i < length; i++) {
        uint8_t byte = data[i];
        
        // 将字节移到高位
        crc ^= (uint32_t)byte << 24;
        
        // 处理每一位
        for (j = 0; j < 8; j++) {
            if (crc & 0x80000000) {
                crc = (crc << 1) ^ CRC32_POLYNOMIAL;
            } else {
                crc = crc << 1;
            }
        }
    }
    
    return crc;
}

/**
 * @brief 验证应用程序的CRC
 * @param app_start_addr 应用程序起始地址
 * @param app_size 应用程序总大小 (包括CRC的4字节)
 * @return true: 验证通过, false: 验证失败
 */
bool verify_app_crc(uint32_t app_start_addr, uint32_t app_size)
{
    // 参数检查
    if (app_size < 4) {
        printf("CRC check failed: firmware size too small (%lu bytes)\r\n", app_size);
        return false;
    }
    
    if (app_size > MAX_APP_SIZE) {
        printf("CRC check failed: firmware size exceeds limit (%lu > %lu)\r\n", app_size, MAX_APP_SIZE);
        return false;
    }
    
    // 计算应用程序数据大小 (总大小 - CRC的4字节)
    uint32_t app_data_size = app_size - 4;
    
    printf("Starting CRC check...\r\n");
    printf("App address: 0x%08lX\r\n", app_start_addr);
    printf("App size: %lu bytes\r\n", app_data_size);
    
    // 获取存储在末尾的CRC值 (小端格式)
    uint8_t *crc_addr = (uint8_t *)(app_start_addr + app_data_size);
    uint32_t stored_crc = 0;
    
    // 从小端字节序转换为32位值
    stored_crc = (uint32_t)crc_addr[0] |
                 ((uint32_t)crc_addr[1] << 8) |
                 ((uint32_t)crc_addr[2] << 16) |
                 ((uint32_t)crc_addr[3] << 24);
    
    printf("Stored CRC: 0x%08lX\r\n", stored_crc);
    
    // 计算应用程序数据的CRC (不包括末尾的CRC值)
    uint32_t calculated_crc = calculate_crc32((uint8_t *)app_start_addr, app_data_size);
    
    printf("Calculated CRC: 0x%08lX\r\n", calculated_crc);
    
    // 比较CRC值
    if (stored_crc == calculated_crc) {
        printf("CRC check passed!\r\n");
        return true;
    } else {
        printf("CRC check failed! (Stored: 0x%08lX, Calculated: 0x%08lX)\r\n", stored_crc, calculated_crc);
        return false;
    }
}

/**
 * @brief 扫描Flash找到实际的应用程序大小
 * @param start_addr 扫描起始地址
 * @param max_size 最大扫描大小
 * @return 实际大小，如果无法确定则返回0
 */
uint32_t scan_app_size(uint32_t start_addr, uint32_t max_size)
{
    uint32_t size = 0;
    uint8_t *ptr = (uint8_t *)start_addr;
    
    // 从后往前扫描，找到第一个非0xFF的位置
    for (uint32_t i = max_size; i > 0; i--) {
        if (ptr[i - 1] != 0xFF) {
            size = i;
            break;
        }
    }
    
    // 检查是否找到了数据
    if (size == 0) {
        printf("Scan failed: no valid data found\r\n");
        return 0;
    }
    
    // 确保大小是4的倍数（CRC对齐）
    if (size % 4 != 0) {
        size = ((size / 4) + 1) * 4;
    }
    
    printf("Scanned app size: %lu bytes\r\n", size);
    return size;
}

/**
 * @brief 获取应用程序的实际大小
 * @return 应用程序大小 (字节), 0表示无效
 */
uint32_t get_app_size(void)
{
    // 方法1: 使用固定大小（从编译信息得知）
    // 当前应用程序编译大小为 38668 字节 (38664 + 4 CRC)
    uint32_t expected_size = 38684;
    
    // 验证这个大小是否合理
    if (expected_size <= MAX_APP_SIZE) {
        printf("Using expected app size: %lu bytes\r\n", expected_size);
        return expected_size;
    }
    
    // 方法2: 扫描Flash内容来确定大小
    printf("Expected size exceeds limit, scanning Flash...\r\n");
    return scan_app_size(APP_BASE_ADDRESS, MAX_APP_SIZE);
}

/**
 * @brief 检查应用程序是否有效（包括向量表和CRC验证）
 * @return true: 应用程序有效, false: 应用程序无效
 */
bool is_app_valid(void)
{
    printf("==========================================\r\n");
    printf("Starting app integrity check...\r\n");
    
    // 1. 检查向量表（栈指针）
    uint32_t stack_ptr = *(volatile uint32_t*)APP_BASE_ADDRESS;
    if ((stack_ptr & 0x2FFE0000) != 0x20000000) {
        printf("Vector table check failed: invalid stack pointer 0x%08lX\r\n", stack_ptr);
        return false;
    }
    printf("Vector table check passed: stack pointer 0x%08lX\r\n", stack_ptr);
    
    // 2. 检查复位向量
    uint32_t reset_vector = *(volatile uint32_t*)(APP_BASE_ADDRESS + 4);
    if ((reset_vector & 0xFF000000) != 0x08000000) {
        printf("Vector table check failed: invalid reset vector 0x%08lX\r\n", reset_vector);
        return false;
    }
    printf("Vector table check passed: reset vector 0x%08lX\r\n", reset_vector);
    
    // 3. 获取应用程序大小
    uint32_t app_size = get_app_size();
    if (app_size == 0 || app_size < 4) {
        printf("App size check failed: %lu bytes\r\n", app_size);
        return false;
    }
    
    // 4. 进行CRC验证
    bool crc_valid = verify_app_crc(APP_BASE_ADDRESS, app_size);
    
    if (crc_valid) {
        printf("App integrity check passed!\r\n");
        printf("==========================================\r\n");
        return true;
    } else {
        printf("App integrity check failed!\r\n");
        printf("==========================================\r\n");
        return false;
    }
}

/**
 * @brief 验证固件更新完成后的完整性
 * @param total_size 接收到的固件总大小
 * @return true: 验证通过, false: 验证失败
 */
bool verify_firmware_after_update(uint32_t total_size)
{
    printf("==========================================\r\n");
    printf("Firmware update completed, starting verification...\r\n");
    printf("Received firmware size: %lu bytes\r\n", total_size);
    
    // 检查大小是否合理
    if (total_size < 4 || total_size > MAX_APP_SIZE) {
        printf("Firmware size is not reasonable: %lu bytes\r\n", total_size);
        return false;
    }
    
    // 进行CRC验证
    bool result = verify_app_crc(APP_BASE_ADDRESS, total_size);
    
    if (result) {
        printf("Firmware verification passed! Safe to start the application.\r\n");
    } else {
        printf("Firmware verification failed! Please re-burn the firmware.\r\n");
    }
    
    printf("==========================================\r\n");
    return result;
}
