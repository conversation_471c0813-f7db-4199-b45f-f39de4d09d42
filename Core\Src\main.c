/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "can.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "canopen.h"
#include "iap.h"
#include "stmflash.h"
#include "sys.h"
#include "stdio.h"
#include "app_crc_verify.h"  // 添加CRC验证支持
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
    uint8_t bit_new = 0;					//接收到程序标志
    uint8_t bit_10s = 0;
    uint8_t t = 0, clearflag = 0;
    uint8_t firmware_update_completed = 0;  //固件更新完成标志
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */
  // 	__enable_irq();
  // __set_PRIMASK(0);
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART2_UART_Init();
  MX_CAN_Init();
  /* USER CODE BEGIN 2 */
  CAN_User_Init(&hcan);
  firmware_stream_init(); // 初始化流式固件更新
  printf("========================================\r\n");
  printf("STM32 Bootloader with CRC Verification\r\n");
  printf("========================================\r\n");
  
  // 启动时检查应用程序状态
  if (is_app_valid()) {
      printf("Application status: VALID (CRC verified)\r\n");
  } else {
      printf("Application status: INVALID or not found\r\n");
  }
  
  printf("Bootloader ready, waiting for commands...\r\n");
  printf("CAN Commands:\r\n");
  printf("  ID:0x101, Data:0x01 - Jump to APP (with CRC check)\r\n");
  printf("  ID:0x101, Data:0x02 - Erase Flash (108KB)\r\n");
  printf("========================================\r\n");
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    // 处理CAN命令
    if (jump_to_app_cmd) {
        jump_to_app_cmd = 0;
        printf("Processing jump to APP command...\r\n");

        // 使用带头部的跳转函数 (自动处理20字节头部)
        printf("Starting user application with firmware header support...\r\n");
        iap_load_app_with_header(); // 执行带头部的APP代码(自动跳过头部并验证CRC)
    }
    
    // 处理擦除Flash命令
    if (erase_flash_cmd) {
        erase_flash_cmd = 0;
        printf("Processing erase Flash command...\r\n");
        iap_erase_app_area(); // 擦除APP区域
        // 擦除完成后重置固件接收状态
        reset_firmware_state();
        printf("Flash erase completed. Ready for new firmware.\r\n");
    }
    
    // 处理Flash写入
    if (flash_write_pending)
    {
        printf("Writing 1KB to Flash at 0x%08X\r\n", current_flash_addr);
        
        // 执行Flash写入
        uint8_t write_result = iap_write_block(current_flash_addr, current_flash_buffer, FLASH_WRITE_SIZE);
        if (write_result == 0) {
            // 写入成功，更新地址和状态
            current_flash_addr += FLASH_WRITE_SIZE;
            
            // 计算并显示写入进度（写入成功后才计算）
            uint32_t written_bytes = current_flash_addr - FLASH_APP1_ADDR;
            uint32_t written_kb = written_bytes / 1024;
            uint32_t written_bytes_remainder = written_bytes % 1024;
            printf("Write successful! Written: %d bytes (%d.%03dKB), Total received: %d bytes\r\n", 
                   written_bytes, written_kb, written_bytes_remainder, total_received_bytes);
        } else {
            printf("Flash write failed! Error code: %d\r\n", write_result);
            // 写入失败，重置状态
            reset_firmware_state();
        }
        
        flash_write_pending = 0;
    }
    
    // 检查接收超时
    // 只有在明确开始固件传输后才检查超时
    if (check_receive_timeout() && !firmware_update_completed)
    {
        if (CAN1_RX_CNT > 0 || total_received_bytes > 0)
        {
            // 写入剩余数据
            if (CAN1_RX_CNT > 0) {
                printf("Writing final %d bytes to Flash\r\n", CAN1_RX_CNT);
                iap_write_block(current_flash_addr, current_rx_buffer, CAN1_RX_CNT);
                total_received_bytes += CAN1_RX_CNT;
            }
            
            printf("Firmware reception completed! Total: %d bytes\r\n", total_received_bytes);
            
            // 进行完整的CRC验证
            if (verify_firmware_after_update(total_received_bytes))
            {
                printf("Firmware validation successful!\r\n");
                printf("Firmware update completed! Send CAN command (ID:0x101, Data:0x01) to start APP.\r\n");
                firmware_update_completed = 1;
                // 注意：不设置bit_new=1，需要等待CAN命令
            }
            else
            {
                printf("Firmware validation failed! CRC check failed.\r\n");
                printf("Please re-upload the firmware.\r\n");
                reset_firmware_state(); // 重置状态，准备重新接收
            }
        }
    }
    
    // 只有接收到CAN命令才能跳转到APP
    // 移除自动超时跳转和固件完成自动跳转
    if (bit_10s == 30 && total_received_bytes == 0) {
        // 30秒超时，但不自动跳转，只给出提示
        bit_10s = 0; // 重置计数器
        if (is_app_valid()) {
            printf("Bootloader timeout! Application is valid. Send CAN command (ID:0x101, Data:0x01) to start APP.\r\n");
        } else {
            printf("Bootloader timeout! No valid user application found or CRC check failed.\r\n");
        }
    }
    
    t++;
    HAL_Delay(10);
    if (t == 20)
    {
        t = 0;
        // 始终递增超时计数器（用于提示信息）
        if (total_received_bytes == 0) {
            bit_10s++;
        } else {
            // 正在接收固件，重置超时计数器
            bit_10s = 0;
        }
        
        if (clearflag)
        {
            clearflag--;
            if (clearflag == 0)
                printf("Clear display!\r\n");
        }
    }

    }
  
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
